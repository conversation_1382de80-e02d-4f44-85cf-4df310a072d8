# Batch Word Document Processor

This Python script processes Microsoft Word (.docx) files in batch to:
1. **Replace header images** with a specified image file ("Picture 1.png" by default)
2. **Remove all images** from document footers

## Features

- ✅ Processes all .docx files in a directory
- ✅ Creates backup copies of original files (optional but recommended)
- ✅ Comprehensive error handling for corrupted or locked files
- ✅ Progress tracking and detailed logging
- ✅ Dry-run mode to preview changes without modifying files
- ✅ Handles documents with multiple sections
- ✅ Preserves all other document content and formatting

## Prerequisites

- Python 3.6 or higher
- `python-docx` library

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
   
   Or install directly:
   ```bash
   pip install python-docx
   ```

2. **Ensure you have the replacement image:**
   - Place your replacement image file (default: "Picture 1.png") in the same directory as the script
   - The script supports common image formats (PNG, JPEG, etc.)

## Usage

### Basic Usage (Recommended)
```bash
# Process all .docx files with backup creation
python batch_process_docx.py --backup
```

### Advanced Usage
```bash
# Dry run to see what would be processed
python batch_process_docx.py --dry-run

# Use a different replacement image
python batch_process_docx.py --backup --image "my_logo.png"

# Process files in a different directory
python batch_process_docx.py --backup --directory "/path/to/documents"

# Process without creating backups (not recommended)
python batch_process_docx.py
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--backup` | Create backup copies of original files (highly recommended) |
| `--dry-run` | Preview what files would be processed without making changes |
| `--image PATH` | Specify a different replacement image (default: "Picture 1.png") |
| `--directory PATH` | Process files in a different directory (default: current directory) |

## What the Script Does

### Header Processing
1. **Removes** any existing images from document headers
2. **Adds** the specified replacement image to the header
3. **Preserves** all other header content (text, formatting, etc.)

### Footer Processing
1. **Removes** all images from document footers
2. **Preserves** all other footer content (text, page numbers, etc.)

### Safety Features
- **Backup Creation**: Original files are copied to a `backups/` subdirectory
- **Error Handling**: Continues processing even if individual files fail
- **Logging**: Detailed log file (`docx_processing.log`) tracks all operations
- **Dry Run**: Preview mode to see what would be changed

## Output and Logging

The script provides:
- **Console output** with progress updates and summary
- **Log file** (`docx_processing.log`) with detailed information about each operation
- **Backup directory** (`backups/`) containing original files (if `--backup` is used)

### Example Output
```
2024-01-15 10:30:15 - INFO - Found 3 .docx file(s) to process
2024-01-15 10:30:15 - INFO - Progress: 1/3
2024-01-15 10:30:15 - INFO - Created backup: backups/document1_backup.docx
2024-01-15 10:30:16 - INFO - Processing: document1.docx
2024-01-15 10:30:16 - INFO - Successfully processed document1.docx: Added new image to header in section 1; Removed 1 image(s) from footer in section 1

==================================================
PROCESSING SUMMARY
==================================================
Total files found: 3
Successfully processed: 3
Failed: 0
Skipped (dry run): 0

Processing complete! Check the log file for detailed information.
Original files have been backed up in the 'backups' directory.
```

## Troubleshooting

### Common Issues

1. **"python-docx library not found"**
   - Install the library: `pip install python-docx`

2. **"Replacement image not found"**
   - Ensure "Picture 1.png" (or your specified image) is in the same directory as the script
   - Use the `--image` option to specify a different path

3. **"Permission denied" errors**
   - Close any Word documents that might be open
   - Ensure you have write permissions to the directory
   - Run the script as administrator if necessary (Windows)

4. **Files appear corrupted after processing**
   - Use the backup files in the `backups/` directory to restore originals
   - Check the log file for specific error messages

### Recovery
If something goes wrong, you can restore original files from the `backups/` directory:
```bash
# Copy all backup files back to the main directory
cp backups/*_backup.docx .
# Then rename them to remove the "_backup" suffix
```

## Technical Notes

- The script handles documents with multiple sections
- Images are resized to 2 inches width by default (can be modified in the code)
- Temporary Word files (starting with `~$`) are automatically ignored
- The script preserves all document metadata and properties

## Support

If you encounter issues:
1. Check the `docx_processing.log` file for detailed error information
2. Try running with `--dry-run` first to identify potential problems
3. Ensure all Word documents are closed before running the script
4. Verify that your replacement image file exists and is accessible
