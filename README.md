# Word Document Batch Processor

## What This Tool Does

This tool automatically processes multiple Word documents (.docx files) to:
- **Replace any existing images in document headers** with your chosen company logo or image
- **Remove all images from document footers** to create clean, consistent footers

Perfect for standardizing corporate documents, reports, or any collection of Word files that need consistent branding.

## Before You Start

### What You'll Need
1. **Python installed on your computer** (we'll show you how)
2. **Your Word documents** in .docx format
3. **Your replacement image** (logo, header image, etc.) in JPG or PNG format

### File Organization
Create this folder structure on your computer:
```
Your Project Folder/
├── batch_process_docx.py          (the script file)
├── Picture 1.jpg                  (your replacement image)
├── documents/                     (folder for documents to process)
│   ├── document1.docx
│   ├── document2.docx
│   └── document3.docx
└── backups/                       (will be created automatically)
```

## Installation Guide

### Step 1: Install Python
1. Go to [python.org/downloads](https://python.org/downloads)
2. Download Python 3.8 or newer
3. Run the installer and **check the box "Add Python to PATH"**
4. Complete the installation

### Step 2: Install Required Libraries
1. Open Command Prompt (Windows) or Terminal (Mac)
2. Type this command and press Enter:
   ```
   pip install python-docx pillow
   ```
3. Wait for installation to complete

### Step 3: Verify Installation
Type this command to check if everything is installed:
```
python --version
```
You should see something like "Python 3.x.x"

## How to Use the Tool

### Basic Usage (Recommended for Most Users)
1. **Prepare your files:**
   - Put all Word documents in the `documents` folder
   - Make sure your replacement image is named `Picture 1.jpg` in the main folder

2. **Open Command Prompt/Terminal** and navigate to your project folder:
   ```
   cd "path/to/your/project/folder"
   ```

3. **Run the script with backup protection:**
   ```
   python batch_process_docx.py --directory documents --backup
   ```

### Command Options Explained

| Option | What it does | Example |
|--------|-------------|---------|
| `--directory` | Specify which folder contains your documents | `--directory documents` |
| `--backup` | Creates backup copies before making changes (RECOMMENDED) | `--backup` |
| `--dry-run` | Shows what would happen without making changes | `--dry-run` |
| `--image` | Use a different replacement image | `--image "my-logo.png"` |

### Usage Examples

**Test run (see what will happen without making changes):**
```
python batch_process_docx.py --directory documents --dry-run
```

**Process documents with backups (recommended):**
```
python batch_process_docx.py --directory documents --backup
```

**Use a different image:**
```
python batch_process_docx.py --directory documents --backup --image "company-logo.png"
```

## What to Expect

### During Processing
You'll see messages like:
```
2024-01-15 10:30:15 - INFO - Found 5 .docx file(s) to process
2024-01-15 10:30:16 - INFO - Progress: 1/5
2024-01-15 10:30:16 - INFO - Processing: document1.docx
2024-01-15 10:30:17 - INFO - Created backup: backups/document1_backup.docx
```

### After Processing
1. **Summary Report:** Shows how many files were processed successfully
2. **Log File:** `docx_processing.log` contains detailed information
3. **Backup Files:** Original documents saved in `backups` folder (if you used `--backup`)
4. **Modified Documents:** Your original files are updated with new headers and clean footers

### Files Created
- `backups/` folder with original copies of your documents
- `docx_processing.log` with detailed processing information

## Troubleshooting

### "python is not recognized" Error
**Problem:** Python isn't installed or not in your system PATH
**Solution:**
1. Reinstall Python from python.org
2. Make sure to check "Add Python to PATH" during installation
3. Restart your computer

### "No module named 'docx'" Error
**Problem:** Required libraries aren't installed
**Solution:** Run this command:
```
pip install python-docx pillow
```

### "No .docx files found" Message
**Problem:** The script can't find your Word documents
**Solution:**
1. Check that your documents are in .docx format (not .doc)
2. Verify the folder path you specified with `--directory`
3. Make sure documents aren't open in Word

### "Replacement image not found" Error
**Problem:** The script can't find your replacement image
**Solution:**
1. Check the image filename (default is "Picture 1.jpg")
2. Make sure the image is in the same folder as the script
3. Use `--image "your-filename.jpg"` if using a different name

### Documents Won't Open After Processing
**Problem:** Corrupted document (rare)
**Solution:**
1. Use your backup files from the `backups` folder
2. Try processing one document at a time to identify problematic files
3. Ensure documents aren't password-protected

## Tips for Success

1. **Always use `--backup`** to protect your original files
2. **Test with `--dry-run` first** to see what will happen
3. **Close all Word documents** before running the script
4. **Use simple filenames** without special characters
5. **Keep your replacement image small** (under 1MB) for best results

## Getting Help

If you encounter issues:
1. Check the `docx_processing.log` file for detailed error messages
2. Try the troubleshooting steps above
3. Run with `--dry-run` to test without making changes
4. Contact your IT support team with the log file if problems persist

---

*This tool processes Word documents safely with backup protection. Always test with a few documents first before processing large batches.*
