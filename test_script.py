#!/usr/bin/env python3
"""
Test script to verify the batch processor functionality
"""

import os
import sys
from pathlib import Path

def test_files_exist():
    """Test that required files exist"""
    print("Testing file existence...")
    
    # Check for the main script
    if not Path("batch_process_docx.py").exists():
        print("❌ batch_process_docx.py not found")
        return False
    print("✅ batch_process_docx.py found")
    
    # Check for the replacement image
    if not Path("Picture 1.png").exists():
        print("❌ Picture 1.png not found")
        return False
    print("✅ Picture 1.png found")
    
    # Check for .docx files
    docx_files = list(Path(".").glob("*.docx"))
    docx_files = [f for f in docx_files if not f.name.startswith("~$")]
    
    if not docx_files:
        print("❌ No .docx files found")
        return False
    print(f"✅ Found {len(docx_files)} .docx file(s):")
    for file in docx_files:
        print(f"   - {file.name}")
    
    return True

def test_import():
    """Test that required libraries can be imported"""
    print("\nTesting library imports...")
    
    try:
        from docx import Document
        print("✅ python-docx library imported successfully")
        return True
    except ImportError:
        print("❌ python-docx library not available")
        print("   Install with: pip install python-docx")
        return False

def main():
    """Run all tests"""
    print("="*50)
    print("BATCH DOCX PROCESSOR - PRE-FLIGHT CHECK")
    print("="*50)
    
    all_tests_passed = True
    
    # Test file existence
    if not test_files_exist():
        all_tests_passed = False
    
    # Test imports
    if not test_import():
        all_tests_passed = False
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("✅ ALL TESTS PASSED - Ready to process documents!")
        print("\nRecommended next steps:")
        print("1. Run a dry-run first: python3 batch_process_docx.py --dry-run")
        print("2. Process with backups: python3 batch_process_docx.py --backup")
    else:
        print("❌ SOME TESTS FAILED - Please fix the issues above")
    print("="*50)

if __name__ == "__main__":
    main()
