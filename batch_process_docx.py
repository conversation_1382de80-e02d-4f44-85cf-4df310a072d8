#!/usr/bin/env python3
"""
Batch Word Document Processor

This script processes all .docx files in the current directory to:
1. Replace any existing image in the document header with "Picture 1.png"
2. Remove any images present in the document footer

Requirements:
- python-docx library: pip install python-docx
- Picture 1.png file in the same directory as this script

Usage:
    python batch_process_docx.py [--backup] [--dry-run]
    
Options:
    --backup    Create backup copies of original files (recommended)
    --dry-run   Show what would be processed without making changes
"""

import os
import sys
import shutil
import argparse
from pathlib import Path
from typing import List, Tuple
import logging

try:
    from PIL import Image as PILImage
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.section import WD_HEADER_FOOTER
except ImportError:
    print("Error: python-docx library not found.")
    print("Please install it using: pip install python-docx")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('docx_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DocxProcessor:
    """Handles processing of Word documents for header/footer image operations."""

    def __init__(self, replacement_image_path: str, force_exact_dimensions: bool = False):
        self.replacement_image_path = Path(replacement_image_path)
        self.force_exact_dimensions = force_exact_dimensions
        if not self.replacement_image_path.exists():
            raise FileNotFoundError(f"Replacement image not found: {replacement_image_path}")
    
    def find_docx_files(self, directory: str) -> List[Path]:
        """Find all .docx files in the specified directory."""
        directory_path = Path(directory)
        docx_files = list(directory_path.glob("*.docx"))
        # Filter out temporary files (starting with ~$)
        docx_files = [f for f in docx_files if not f.name.startswith("~$")]
        return docx_files
    
    def create_backup(self, file_path: Path) -> Path:
        """Create a backup copy of the original file."""
        backup_dir = file_path.parent / "backups"
        backup_dir.mkdir(exist_ok=True)
        backup_path = backup_dir / f"{file_path.stem}_backup{file_path.suffix}"
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def remove_images_from_element(self, element) -> int:
        """Remove all images from a given element (header/footer). Returns count of removed images."""
        removed_count = 0

        # Find all drawing elements (which contain images)
        for paragraph in element.paragraphs:
            runs_to_remove = []
            for run in paragraph.runs:
                # Check for inline shapes (images) using a more compatible approach
                run_element = run._element

                # Look for drawing elements that typically contain images
                drawings = run_element.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}drawing')
                inline_shapes = run_element.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}object')

                if drawings or inline_shapes:
                    runs_to_remove.append(run)
                    removed_count += 1

            # Remove runs that contain images
            for run in runs_to_remove:
                paragraph._element.remove(run._element)

        return removed_count
    
    def get_image_dimensions(self, image_path: Path) -> Tuple[float, float]:
        """Get image dimensions and calculate proper sizing. Returns (width_inches, height_inches)."""
        target_width_inches = 0.76
        target_height_inches = 2.44

        if PIL_AVAILABLE:
            try:
                with PILImage.open(image_path) as img:
                    original_width, original_height = img.size
                    original_aspect_ratio = original_width / original_height
                    target_aspect_ratio = target_width_inches / target_height_inches

                    logger.info(f"Original image: {original_width}x{original_height} pixels, aspect ratio: {original_aspect_ratio:.3f}")
                    logger.info(f"Target dimensions: {target_width_inches}\"x{target_height_inches}\", aspect ratio: {target_aspect_ratio:.3f}")

                    # If aspect ratios are very different, we need to choose how to fit
                    if abs(original_aspect_ratio - target_aspect_ratio) > 0.1:
                        logger.warning(f"Aspect ratio mismatch! Original: {original_aspect_ratio:.3f}, Target: {target_aspect_ratio:.3f}")

                        # Option 1: Fit by width (may be shorter than target height)
                        fit_by_width_height = target_width_inches / original_aspect_ratio

                        # Option 2: Fit by height (may be wider than target width)
                        fit_by_height_width = target_height_inches * original_aspect_ratio

                        logger.info(f"Fit by width: {target_width_inches}\"x{fit_by_width_height:.3f}\"")
                        logger.info(f"Fit by height: {fit_by_height_width:.3f}\"x{target_height_inches}\"")

                        # Choose the method based on user preference
                        if self.force_exact_dimensions:
                            logger.warning("FORCING exact dimensions - image will be distorted!")
                            return target_width_inches, target_height_inches
                        elif fit_by_width_height <= target_height_inches:
                            logger.info("Using width-based fitting (maintains aspect ratio)")
                            return target_width_inches, fit_by_width_height
                        else:
                            logger.info("Using height-based fitting (maintains aspect ratio)")
                            return fit_by_height_width, target_height_inches
                    else:
                        logger.info("Aspect ratios match well, using target dimensions")
                        return target_width_inches, target_height_inches

            except Exception as e:
                logger.warning(f"Could not analyze image with PIL: {e}")
        else:
            logger.warning("PIL not available for image analysis, using target dimensions")

        return target_width_inches, target_height_inches

    def add_image_to_header(self, header, image_path: Path) -> bool:
        """Add image to header with proper aspect ratio handling. Returns True if successful."""
        try:
            # Get or create the first paragraph in header
            if not header.paragraphs:
                paragraph = header.add_paragraph()
            else:
                paragraph = header.paragraphs[0]
                # Clear existing runs but keep the paragraph
                for run in paragraph.runs:
                    paragraph._element.remove(run._element)

            # Calculate proper dimensions
            width_inches, height_inches = self.get_image_dimensions(image_path)

            # Add new image to the paragraph
            run = paragraph.add_run()

            # Try different sizing approaches
            success = False

            # Method 1: Use calculated dimensions
            try:
                run.add_picture(str(image_path), width=Inches(width_inches), height=Inches(height_inches))
                logger.info(f"Added image with calculated dimensions: {width_inches:.3f}\"x{height_inches:.3f}\"")
                success = True
            except Exception as e1:
                logger.warning(f"Calculated dimensions failed: {e1}")

                # Method 2: Try width-only (maintains aspect ratio)
                try:
                    paragraph._element.remove(run._element)
                    run = paragraph.add_run()
                    run.add_picture(str(image_path), width=Inches(width_inches))
                    logger.info(f"Added image with width-only: {width_inches:.3f}\" (aspect ratio maintained)")
                    success = True
                except Exception as e2:
                    logger.warning(f"Width-only failed: {e2}")

                    # Method 3: Default approach
                    try:
                        paragraph._element.remove(run._element)
                        run = paragraph.add_run()
                        run.add_picture(str(image_path))
                        logger.info("Added image with default sizing")
                        success = True
                    except Exception as e3:
                        logger.error(f"All sizing methods failed: {e3}")

            return success

        except Exception as e:
            logger.error(f"Failed to add image to header: {e}")
            return False

    def process_document(self, file_path: Path) -> Tuple[bool, str]:
        """
        Process a single document for header image replacement and footer image removal.
        Returns (success, message).
        """
        try:
            logger.info(f"Processing: {file_path.name}")

            # Open the document
            doc = Document(str(file_path))

            changes_made = False
            messages = []

            # Process each section (documents can have multiple sections)
            for section_idx, section in enumerate(doc.sections):
                # Process header
                if section.header:
                    # Remove existing images from header
                    removed_header_images = self.remove_images_from_element(section.header)
                    if removed_header_images > 0:
                        messages.append(f"Removed {removed_header_images} image(s) from header in section {section_idx + 1}")
                        changes_made = True

                    # Add new image to header
                    if self.add_image_to_header(section.header, self.replacement_image_path):
                        messages.append(f"Added new image to header in section {section_idx + 1}")
                        changes_made = True

                # Process footer - remove all images
                if section.footer:
                    removed_footer_images = self.remove_images_from_element(section.footer)
                    if removed_footer_images > 0:
                        messages.append(f"Removed {removed_footer_images} image(s) from footer in section {section_idx + 1}")
                        changes_made = True

            if changes_made:
                # Save the document
                doc.save(str(file_path))
                message = f"Successfully processed {file_path.name}: " + "; ".join(messages)
                logger.info(message)
                return True, message
            else:
                message = f"No changes needed for {file_path.name}"
                logger.info(message)
                return True, message

        except Exception as e:
            error_msg = f"Error processing {file_path.name}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def batch_process(self, directory: str, create_backups: bool = True, dry_run: bool = False) -> dict:
        """
        Process all .docx files in the directory.
        Returns a summary dictionary with processing results.
        """
        docx_files = self.find_docx_files(directory)

        if not docx_files:
            logger.warning("No .docx files found in the directory")
            return {"total": 0, "processed": 0, "failed": 0, "skipped": 0}

        logger.info(f"Found {len(docx_files)} .docx file(s) to process")

        if dry_run:
            logger.info("DRY RUN MODE - No files will be modified")
            for file_path in docx_files:
                logger.info(f"Would process: {file_path.name}")
            return {"total": len(docx_files), "processed": 0, "failed": 0, "skipped": len(docx_files)}

        results = {"total": len(docx_files), "processed": 0, "failed": 0, "skipped": 0}

        for i, file_path in enumerate(docx_files, 1):
            logger.info(f"Progress: {i}/{len(docx_files)}")

            try:
                # Create backup if requested
                if create_backups:
                    backup_path = self.create_backup(file_path)
                    logger.info(f"Created backup: {backup_path}")

                # Process the document
                success, message = self.process_document(file_path)

                if success:
                    results["processed"] += 1
                else:
                    results["failed"] += 1

            except Exception as e:
                logger.error(f"Unexpected error with {file_path.name}: {e}")
                results["failed"] += 1

        return results


def main():
    """Main function to handle command line arguments and execute processing."""
    parser = argparse.ArgumentParser(
        description="Batch process Word documents to replace header images and remove footer images"
    )
    parser.add_argument(
        "--backup",
        action="store_true",
        help="Create backup copies of original files (recommended)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be processed without making changes"
    )
    parser.add_argument(
        "--image",
        default="Picture 1.jpg",
        help="Path to replacement image (default: Picture 1.jpg)"
    )
    parser.add_argument(
        "--directory",
        default=".",
        help="Directory containing .docx files (default: current directory)"
    )
    parser.add_argument(
        "--force-exact-size",
        action="store_true",
        help="Force exact dimensions (0.76\"x2.44\") even if it distorts the image"
    )

    args = parser.parse_args()

    try:
        # Initialize processor
        processor = DocxProcessor(args.image, force_exact_dimensions=args.force_exact_size)

        # Process documents
        results = processor.batch_process(
            directory=args.directory,
            create_backups=args.backup,
            dry_run=args.dry_run
        )

        # Print summary
        print("\n" + "="*50)
        print("PROCESSING SUMMARY")
        print("="*50)
        print(f"Total files found: {results['total']}")
        print(f"Successfully processed: {results['processed']}")
        print(f"Failed: {results['failed']}")
        print(f"Skipped (dry run): {results['skipped']}")

        if results['failed'] > 0:
            print(f"\nCheck 'docx_processing.log' for detailed error information.")

        if not args.dry_run and results['processed'] > 0:
            print(f"\nProcessing complete! Check the log file for detailed information.")
            if args.backup:
                print("Original files have been backed up in the 'backups' directory.")

    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
