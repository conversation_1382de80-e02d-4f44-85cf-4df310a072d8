# 📁 Recommended Folder Structure Guide

## 🎯 **Organized Approach for 600+ Documents**

Instead of mixing scripts and documents, use this clean folder structure:

```
BBVA_documents/
├── 🔧 SCRIPTS & CONFIG
│   ├── batch_process_docx.py          # Main processing script
│   ├── Picture 1.jpg                  # Replacement image
│   ├── requirements.txt               # Dependencies
│   ├── README.md                      # Full documentation
│   ├── QUICK_START.md                 # Quick reference
│   ├── test_script.py                 # Testing tools
│   └── test_image_sizing.py           # Image analysis
│
├── 📂 documents/                      # INPUT FOLDER
│   ├── document1.docx                 # Put your 600+ files here
│   ├── document2.docx
│   ├── document3.docx
│   └── ... (all your .docx files)
│
├── 📂 processed/                      # OUTPUT FOLDER (optional)
│   └── (processed files can be moved here)
│
├── 📂 backups/                        # BACKUP FOLDER (auto-created)
│   ├── document1_backup.docx
│   ├── document2_backup.docx
│   └── ... (automatic safety backups)
│
└── 📊 docx_processing.log             # Processing history
```

## 🚀 **Updated Usage Commands:**

### **Process Documents in 'documents' Folder:**
```bash
# Navigate to main directory
cd "/Users/<USER>/Desktop/BBVA_documents"

# Process all files in documents/ folder
python3 batch_process_docx.py --backup --directory documents
```

### **Alternative Processing Options:**

#### **1. Test First (Safe Preview):**
```bash
python3 batch_process_docx.py --dry-run --directory documents
```

#### **2. Process and Move to 'processed' Folder:**
```bash
# Process files
python3 batch_process_docx.py --backup --directory documents

# Then move processed files (optional)
mv documents/*.docx processed/
```

#### **3. Process Different Folder:**
```bash
python3 batch_process_docx.py --backup --directory "/path/to/other/folder"
```

## ✅ **Benefits of This Structure:**

| Benefit | Description |
|---------|-------------|
| 🗂️ **Organization** | Scripts separate from documents |
| 🛡️ **Safety** | Clear backup and original separation |
| 📊 **Scalability** | Easy to handle 600+ files |
| 🔍 **Tracking** | Know what's processed vs. pending |
| 🚀 **Professional** | Industry-standard project layout |
| 🧹 **Clean** | No clutter in main directory |

## 📋 **Setup Steps:**

1. **Create folders** (already done):
   ```bash
   mkdir -p documents processed
   ```

2. **Move your .docx files** to `documents/` folder:
   ```bash
   mv *.docx documents/
   ```

3. **Run processing**:
   ```bash
   python3 batch_process_docx.py --backup --directory documents
   ```

## 🎯 **For Your 600+ Files Workflow:**

1. **📥 Add files**: Put all your .docx files in `documents/` folder
2. **🧪 Test**: `python3 batch_process_docx.py --dry-run --directory documents`
3. **🚀 Process**: `python3 batch_process_docx.py --backup --directory documents`
4. **✅ Verify**: Check results in `documents/` and backups in `backups/`
5. **📁 Organize**: Optionally move processed files to `processed/` folder

## 🔄 **Current Status:**

✅ Folders created and organized
✅ Sample documents moved to `documents/` folder  
✅ Ready for your 600+ file batch processing

This structure will make managing your large document batch much more organized and professional! 🎉
